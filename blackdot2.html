<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Rotating Square with Dot Counter</title>
    <style>
        body {
            display: flex;
            flex-direction: column;
            align-items: center;
            justify-content: center;
            min-height: 100vh;
            background: linear-gradient(135deg, #1a2a6c, #b21f1f, #1a2a6c);
            margin: 0;
            padding: 20px;
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            color: white;
            overflow-x: hidden;
        }
        
        .container {
            display: flex;
            flex-direction: column;
            align-items: center;
            gap: 30px;
            max-width: 800px;
            width: 100%;
        }
        
        .header {
            text-align: center;
            margin-bottom: 10px;
        }
        
        .header h1 {
            font-size: 2.5rem;
            margin-bottom: 10px;
            text-shadow: 0 2px 4px rgba(0, 0, 0, 0.5);
            background: linear-gradient(45deg, #ff8a00, #e52e71);
            -webkit-background-clip: text;
            -webkit-text-fill-color: transparent;
        }
        
        .header p {
            font-size: 1.1rem;
            opacity: 0.9;
            max-width: 600px;
        }
        
        .animation-area {
            position: relative;
            width: 300px;
            height: 300px;
            display: flex;
            justify-content: center;
            align-items: center;
            perspective: 800px;
        }
        
        .squares-container {
            position: relative;
            width: 200px;
            height: 200px;
        }
        
        .static-square {
            position: absolute;
            width: 150px;
            height: 150px;
            background: rgba(255, 255, 255, 0.1);
            border: 2px solid rgba(255, 255, 255, 0.3);
            border-radius: 8px;
            box-shadow: 0 0 20px rgba(0, 0, 0, 0.2);
            display: flex;
            justify-content: center;
            align-items: center;
            font-size: 1.5rem;
            font-weight: bold;
            color: rgba(255, 255, 255, 0.7);
            left: 0;
            top: 0;
        }
        
        .rotating-square {
            position: absolute;
            width: 150px;
            height: 150px;
            background: linear-gradient(45deg, #ff8a00, #e52e71);
            border: 2px solid white;
            border-radius: 8px;
            display: flex;
            justify-content: center;
            align-items: center;
            transform-origin: center;
            left: 0;
            top: 0;
            box-shadow: 0 0 30px rgba(229, 46, 113, 0.5);
        }
        
        .rotating-dot {
            position: absolute;
            width: 15px;
            height: 15px;
            background: #000;
            border-radius: 50%;
            top: 10px;
            right: 10px;
            box-shadow: 0 0 10px rgba(0, 0, 0, 0.8);
        }
        
        .fixed-dot {
            position: absolute;
            width: 15px;
            height: 15px;
            background: #00ff9d;
            border-radius: 50%;
            top: 10px;
            right: 10px;
            box-shadow: 0 0 10px rgba(0, 255, 157, 0.7);
            z-index: 10;
        }
        
        .counter-display {
            font-size: 1.8rem;
            margin: 20px 0;
            text-align: center;
            background: rgba(0, 0, 0, 0.3);
            padding: 15px 30px;
            border-radius: 10px;
            min-width: 250px;
            box-shadow: 0 5px 15px rgba(0, 0, 0, 0.3);
        }
        
        .counter-value {
            font-size: 2.5rem;
            font-weight: bold;
            color: #00ff9d;
            text-shadow: 0 0 10px rgba(0, 255, 157, 0.7);
        }
        
        .controls {
            display: flex;
            gap: 15px;
            flex-wrap: wrap;
            justify-content: center;
            margin-top: 10px;
        }
        
        .control-btn {
            padding: 12px 25px;
            font-size: 1rem;
            background: rgba(0, 0, 0, 0.3);
            color: white;
            border: 2px solid #00ff9d;
            border-radius: 50px;
            cursor: pointer;
            transition: all 0.3s ease;
            min-width: 120px;
            font-weight: bold;
        }
        
        .control-btn:hover {
            background: rgba(0, 255, 157, 0.2);
            transform: translateY(-3px);
            box-shadow: 0 5px 15px rgba(0, 0, 0, 0.3);
        }
        
        .control-btn:active {
            transform: translateY(1px);
        }
        
        .speed-control {
            display: flex;
            align-items: center;
            gap: 15px;
            margin-top: 15px;
            background: rgba(0, 0, 0, 0.3);
            padding: 15px 25px;
            border-radius: 50px;
        }
        
        .speed-control label {
            font-size: 1rem;
        }
        
        .speed-control input {
            width: 150px;
        }
        
        .explanation {
            margin-top: 30px;
            background: rgba(0, 0, 0, 0.3);
            padding: 20px;
            border-radius: 15px;
            max-width: 600px;
        }
        
        .explanation h3 {
            color: #00ff9d;
            margin-top: 0;
        }
        
        .explanation ul {
            padding-left: 20px;
        }
        
        .explanation li {
            margin-bottom: 10px;
            line-height: 1.5;
        }
        
        .pulse {
            animation: pulse 1s infinite;
        }
        
        @keyframes pulse {
            0% { transform: scale(1); }
            50% { transform: scale(1.1); }
            100% { transform: scale(1); }
        }
        
        @media (max-width: 600px) {
            .animation-area {
                width: 250px;
                height: 250px;
            }
            
            .header h1 {
                font-size: 2rem;
            }
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>Rotating Square with Dot Counter</h1>
            <p>Watch the rotating square complete full rotations. Each time the black dot aligns with the green dot, a rotation is counted!</p>
        </div>
        
        <div class="animation-area">
            <div class="squares-container">
                <div class="static-square">STATIC</div>
                <div class="static-square" style="left: 80px;">OVERLAPPED</div>
                <div class="rotating-square">
                    <div class="rotating-dot"></div>
                </div>
                <div class="fixed-dot"></div>
            </div>
        </div>
        
        <div class="counter-display">
            Rotations Completed: <span id="counter" class="counter-value">0</span>
        </div>
        
        <div class="controls">
            <button id="startBtn" class="control-btn pulse">Start</button>
            <button id="pauseBtn" class="control-btn">Pause</button>
            <button id="resetBtn" class="control-btn">Reset</button>
            <button id="reverseBtn" class="control-btn">Reverse</button>
        </div>
        
        <div class="speed-control">
            <label for="speed">Rotation Speed:</label>
            <input type="range" id="speed" min="1" max="10" value="5">
        </div>
        
        <div class="explanation">
            <h3>How This Works:</h3>
            <ul>
                <li>The <strong style="color:#00ff9d">green dot</strong> is fixed to the overlapped static square</li>
                <li>The <strong>black dot</strong> is attached to the rotating square</li>
                <li>Each time the rotating square completes a full 360° rotation, the dots align</li>
                <li>When the dots meet, the rotation counter increases by 1</li>
                <li>Use the controls to start, pause, reset, reverse, or adjust rotation speed</li>
            </ul>
        </div>
    </div>

    <script>
        // DOM elements
        const rotatingSquare = document.querySelector('.rotating-square');
        const counterElement = document.getElementById('counter');
        const startBtn = document.getElementById('startBtn');
        const pauseBtn = document.getElementById('pauseBtn');
        const resetBtn = document.getElementById('resetBtn');
        const reverseBtn = document.getElementById('reverseBtn');
        const speedControl = document.getElementById('speed');
        
        // Animation state
        let animationId = null;
        let rotation = 0;
        let counter = 0;
        let isAnimating = false;
        let rotationSpeed = 2; // degrees per frame
        let direction = 1; // 1 for clockwise, -1 for counterclockwise
        
        // Initialize animation
        function init() {
            startBtn.addEventListener('click', startAnimation);
            pauseBtn.addEventListener('click', pauseAnimation);
            resetBtn.addEventListener('click', resetAnimation);
            reverseBtn.addEventListener('click', reverseAnimation);
            speedControl.addEventListener('input', updateSpeed);
            
            // Set initial state
            pauseAnimation();
            updateCounter();
        }
        
        // Animation function
        function animate() {
            rotation += rotationSpeed * direction;
            
            // Normalize rotation to 0-360 range
            rotation = rotation % 360;
            if (rotation < 0) rotation += 360;
            
            // Apply rotation to the square
            rotatingSquare.style.transform = `rotate(${rotation}deg)`;
            
            // Check for completed rotation
            if (Math.floor(rotation) % 360 === 0 && rotation !== 0) {
                counter++;
                updateCounter();
                
                // Visual feedback
                counterElement.classList.add('pulse');
                setTimeout(() => counterElement.classList.remove('pulse'), 300);
            }
            
            // Continue animation
            animationId = requestAnimationFrame(animate);
        }
        
        // Start animation
        function startAnimation() {
            if (isAnimating) return;
            isAnimating = true;
            animate();
            updateButtonStates();
        }
        
        // Pause animation
        function pauseAnimation() {
            if (!isAnimating) return;
            cancelAnimationFrame(animationId);
            isAnimating = false;
            updateButtonStates();
        }
        
        // Reset animation
        function resetAnimation() {
            pauseAnimation();
            rotation = 0;
            counter = 0;
            rotatingSquare.style.transform = `rotate(0deg)`;
            updateCounter();
            updateButtonStates();
        }
        
        // Reverse animation direction
        function reverseAnimation() {
            direction *= -1;
            updateButtonStates();
        }
        
        // Update rotation speed
        function updateSpeed() {
            // Map slider value (1-10) to speed (1-5 degrees per frame)
            rotationSpeed = parseInt(speedControl.value) * 0.5;
        }
        
        // Update counter display
        function updateCounter() {
            counterElement.textContent = counter;
        }
        
        // Update button states
        function updateButtonStates() {
            startBtn.textContent = isAnimating ? "Resume" : "Start";
            startBtn.classList.toggle('pulse', !isAnimating);
            reverseBtn.textContent = direction === 1 ? "Reverse" : "Forward";
        }
        
        // Initialize on load
        window.addEventListener('load', init);
    </script>
</body>
</html>