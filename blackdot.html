<!DOCTYPE html>
<html lang="en">
<head>
  <meta charset="UTF-8" />
  <meta name="viewport" content="width=device-width, initial-scale=1.0"/>
  <title>Rotating Square with Dot</title>
  <style>
    body {
      display: flex;
      justify-content: center;
      align-items: center;
      height: 100vh;
      margin: 0;
      background: #f0f0f0;
    }

    .container {
      position: relative;
      width: 400px;
      height: 200px;
    }

    .square {
      width: 100px;
      height: 100px;
      background-color: #4CAF50;
      position: absolute;
    }

    .square1 {
      left: 0;
      top: 50px;
    }

    .square2 {
      left: 150px;
      top: 50px;
      background-color: #2196F3;
    }

    .rotating-square {
      width: 100px;
      height: 100px;
      background-color: rgba(255, 0, 0, 0.7);
      position: absolute;
      left: 150px;
      top: 50px;
      transform-origin: center center;
    }

    .dot {
      width: 10px;
      height: 10px;
      background-color: black;
      border-radius: 50%;
      position: absolute;
      top: -5px;
      right: -5px;
    }
  </style>
</head>
<body>
  <div class="container">
    <div class="square square1"></div>
    <div class="square square2"></div>
    <div class="rotating-square" id="rotatingSquare">
      <div class="dot"></div>
    </div>
  </div>

  <script>
    const rotatingSquare = document.getElementById("rotatingSquare");
    let angle = 0;

    function animate() {
      angle += 1; // degrees
      rotatingSquare.style.transform = `rotate(${angle}deg)`;
      requestAnimationFrame(animate);
    }

    animate();
  </script>
</body>
</html>
