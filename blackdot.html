<!DOCTYPE html>
<html lang="en">
<head>
  <meta charset="UTF-8" />
  <meta name="viewport" content="width=device-width, initial-scale=1.0"/>
  <title>Interactive Rotating Squares</title>
  <style>
    * {
      margin: 0;
      padding: 0;
      box-sizing: border-box;
    }

    body {
      display: flex;
      flex-direction: column;
      justify-content: center;
      align-items: center;
      min-height: 100vh;
      background: linear-gradient(135deg, #0c0c0c 0%, #1a1a1a 100%);
      font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
      color: #ffffff;
      overflow: hidden;
    }

    .title {
      font-size: 2.5rem;
      font-weight: 300;
      margin-bottom: 2rem;
      background: linear-gradient(45deg, #64ffda, #00bcd4);
      -webkit-background-clip: text;
      -webkit-text-fill-color: transparent;
      background-clip: text;
      text-align: center;
      letter-spacing: 2px;
    }

    .controls {
      margin-bottom: 3rem;
      display: flex;
      gap: 1rem;
      align-items: center;
      padding: 1.5rem 2rem;
      background: rgba(255, 255, 255, 0.05);
      border-radius: 20px;
      backdrop-filter: blur(10px);
      border: 1px solid rgba(255, 255, 255, 0.1);
      box-shadow: 0 8px 32px rgba(0, 0, 0, 0.3);
    }

    button {
      padding: 12px 24px;
      font-size: 14px;
      font-weight: 500;
      border: none;
      border-radius: 12px;
      cursor: pointer;
      background: linear-gradient(45deg, #64ffda, #00bcd4);
      color: #000;
      transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
      text-transform: uppercase;
      letter-spacing: 1px;
      position: relative;
      overflow: hidden;
    }

    button::before {
      content: '';
      position: absolute;
      top: 0;
      left: -100%;
      width: 100%;
      height: 100%;
      background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.3), transparent);
      transition: left 0.5s;
    }

    button:hover::before {
      left: 100%;
    }

    button:hover {
      transform: translateY(-2px);
      box-shadow: 0 8px 25px rgba(100, 255, 218, 0.3);
    }

    button:disabled {
      background: rgba(255, 255, 255, 0.1);
      color: rgba(255, 255, 255, 0.3);
      cursor: not-allowed;
      transform: none;
      box-shadow: none;
    }

    .counter {
      font-size: 1.2rem;
      font-weight: 300;
      margin-left: 2rem;
      color: #64ffda;
      text-shadow: 0 0 10px rgba(100, 255, 218, 0.5);
    }

    .container {
      position: relative;
      width: 300px;
      height: 300px;
      background: rgba(255, 255, 255, 0.03);
      border-radius: 20px;
      backdrop-filter: blur(15px);
      border: 1px solid rgba(255, 255, 255, 0.1);
      box-shadow: 0 20px 40px rgba(0, 0, 0, 0.4);
      display: flex;
      justify-content: center;
      align-items: center;
    }

    .square {
      width: 120px;
      height: 120px;
      position: absolute;
      border-radius: 12px;
    }

    .square1 {
      background: linear-gradient(45deg, #1e3c72, #2a5298);
      box-shadow: 0 0 30px rgba(30, 60, 114, 0.4);
      border: 1px solid rgba(255, 255, 255, 0.1);
    }

    .rotating-square {
      width: 120px;
      height: 120px;
      background: linear-gradient(45deg, rgba(255, 64, 129, 0.8), rgba(255, 23, 68, 0.8));
      position: absolute;
      transform-origin: center center;
      border-radius: 12px;
      box-shadow: 0 0 40px rgba(255, 64, 129, 0.3);
      border: 1px solid rgba(255, 255, 255, 0.2);
    }

    .dot {
      width: 12px;
      height: 12px;
      background: radial-gradient(circle, #ffffff, #64ffda);
      border-radius: 50%;
      position: absolute;
      top: -6px;
      right: -6px;
      box-shadow: 0 0 15px rgba(100, 255, 218, 0.8);
      border: 2px solid rgba(255, 255, 255, 0.3);
    }

    .fixed-dot {
      width: 12px;
      height: 12px;
      background: radial-gradient(circle, #ff4081, #ff1744);
      border-radius: 50%;
      position: absolute;
      top: -6px;
      right: -6px;
      box-shadow: 0 0 15px rgba(255, 64, 129, 0.8);
      border: 2px solid rgba(255, 255, 255, 0.3);
    }

    .status {
      margin-top: 2rem;
      font-size: 1.1rem;
      font-weight: 300;
      color: rgba(255, 255, 255, 0.8);
      text-align: center;
      padding: 1rem 2rem;
      background: rgba(255, 255, 255, 0.05);
      border-radius: 15px;
      backdrop-filter: blur(10px);
      border: 1px solid rgba(255, 255, 255, 0.1);
      min-height: 60px;
      display: flex;
      align-items: center;
      justify-content: center;
    }

    @keyframes pulse {
      0%, 100% { transform: scale(1); }
      50% { transform: scale(1.05); }
    }

    .completion-flash {
      animation: pulse 0.3s ease-in-out;
    }

    /* Responsive design */
    @media (max-width: 768px) {
      .title {
        font-size: 2rem;
        margin-bottom: 1.5rem;
      }

      .controls {
        flex-direction: column;
        gap: 0.8rem;
        padding: 1rem;
      }

      .counter {
        margin-left: 0;
        margin-top: 0.5rem;
      }

      .container {
        width: 250px;
        height: 250px;
      }

      .square, .rotating-square {
        width: 100px;
        height: 100px;
      }
    }
  </style>
</head>
<body>
  <h1 class="title">Rotating Squares</h1>

  <div class="controls">
    <button id="startBtn" onclick="startAnimation()">Start</button>
    <button id="pauseBtn" onclick="pauseAnimation()" disabled>Pause</button>
    <button id="resetBtn" onclick="resetAnimation()">Reset</button>
    <span class="counter">Corners: <span id="completionCount">0</span>/4</span>
  </div>

  <div class="container">
    <div class="square square1">
      <div class="fixed-dot"></div>
    </div>
    <div class="rotating-square" id="rotatingSquare">
      <div class="dot"></div>
    </div>
  </div>

  <div class="status" id="status">Ready to start</div></body>
</html>

  <script>
    // Global variables
    const rotatingSquare = document.getElementById("rotatingSquare");
    const startBtn = document.getElementById("startBtn");
    const pauseBtn = document.getElementById("pauseBtn");
    const resetBtn = document.getElementById("resetBtn");
    const completionCountEl = document.getElementById("completionCount");
    const statusEl = document.getElementById("status");

    let angle = 0;
    let isAnimating = false;
    let animationId = null;
    let completionCount = 0;
    let lastCompletionAngle = 0;

    // Initialize corner tracking
    window.passedCorners = new Set();

    // Animation function
    function animate() {
      if (!isAnimating) return;

      angle += 2; // degrees per frame
      rotatingSquare.style.transform = `rotate(${angle}deg)`;

      // Check for completion (full 360 degree rotation)
      checkCompletion();

      animationId = requestAnimationFrame(animate);
    }

    // Check if dot reaches a corner (every 90 degrees)
    function checkCompletion() {
      // Check for exact corner positions (90°, 180°, 270°, 360°/0°)
      const corners = [90, 180, 270, 360];

      for (let corner of corners) {
        // Check if we just passed this corner
        if (angle >= corner && !window.passedCorners.has(corner)) {
          window.passedCorners.add(corner);
          completionCount++;
          completionCountEl.textContent = `${completionCount}/4`;

          // Determine which corner was reached
          const cornerNames = {90: 'Bottom-Right', 180: 'Bottom-Left', 270: 'Top-Left', 360: 'Top-Right'};
          const cornerName = cornerNames[corner];

          statusEl.textContent = `${completionCount} corner${completionCount !== 1 ? 's' : ''} reached! (${cornerName})`;

          // Visual feedback
          flashCompletion();

          // Stop after reaching all 4 corners
          if (completionCount >= 4) {
            setTimeout(() => {
              pauseAnimation();
              statusEl.textContent = `✨ Complete! All 4 corners reached in 360°`;
            }, 100);
          }
          break;
        }
      }
    }

    // Visual feedback for completion
    function flashCompletion() {
      rotatingSquare.classList.add('completion-flash');
      rotatingSquare.style.boxShadow = '0 0 50px rgba(100, 255, 218, 0.8), 0 0 100px rgba(100, 255, 218, 0.4)';

      setTimeout(() => {
        rotatingSquare.classList.remove('completion-flash');
        rotatingSquare.style.boxShadow = '0 0 40px rgba(255, 64, 129, 0.3)';
      }, 400);
    }

    // Start animation
    function startAnimation() {
      isAnimating = true;
      startBtn.disabled = true;
      pauseBtn.disabled = false;
      statusEl.textContent = "Animation running...";
      animate();
    }

    // Pause animation
    function pauseAnimation() {
      isAnimating = false;
      startBtn.disabled = false;
      pauseBtn.disabled = true;
      statusEl.textContent = "Animation paused";
      if (animationId) {
        cancelAnimationFrame(animationId);
      }
    }

    // Reset animation
    function resetAnimation() {
      isAnimating = false;
      angle = 0;
      completionCount = 0;
      lastCompletionAngle = 0;

      // Clear corner tracking
      window.passedCorners.clear();

      rotatingSquare.style.transform = `rotate(0deg)`;
      completionCountEl.textContent = "0/4";
      statusEl.textContent = "Reset complete - Ready to start";

      startBtn.disabled = false;
      pauseBtn.disabled = true;

      if (animationId) {
        cancelAnimationFrame(animationId);
      }
    }

    // Keyboard controls
    document.addEventListener('keydown', function(event) {
      switch(event.code) {
        case 'Space':
          event.preventDefault();
          if (isAnimating) {
            pauseAnimation();
          } else {
            startAnimation();
          }
          break;
        case 'KeyR':
          resetAnimation();
          break;
      }
    });

    // Initialize
    statusEl.textContent = "Ready to start (Space: Start/Pause, R: Reset)";
  </script>
</body>
</html>
