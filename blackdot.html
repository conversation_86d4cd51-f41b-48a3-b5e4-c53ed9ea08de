<!DOCTYPE html>
<html lang="en">
<head>
  <meta charset="UTF-8" />
  <meta name="viewport" content="width=device-width, initial-scale=1.0"/>
  <title>Quantum Rotation Studio | Professional Animation Tool</title>
  <link rel="preconnect" href="https://fonts.googleapis.com">
  <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin>
  <link href="https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700&display=swap" rel="stylesheet">
  <style>
    * {
      margin: 0;
      padding: 0;
      box-sizing: border-box;
    }

    body {
      display: flex;
      flex-direction: column;
      justify-content: center;
      align-items: center;
      min-height: 100vh;
      background:
        radial-gradient(circle at 20% 80%, rgba(120, 119, 198, 0.3) 0%, transparent 50%),
        radial-gradient(circle at 80% 20%, rgba(255, 119, 198, 0.3) 0%, transparent 50%),
        radial-gradient(circle at 40% 40%, rgba(120, 219, 255, 0.2) 0%, transparent 50%),
        linear-gradient(135deg, #0a0a0a 0%, #1a1a1a 50%, #0f0f0f 100%);
      font-family: 'Inter', -apple-system, BlinkMacSystemFont, 'Segoe UI', sans-serif;
      color: #ffffff;
      overflow: hidden;
      position: relative;
    }

    body::before {
      content: '';
      position: absolute;
      top: 0;
      left: 0;
      right: 0;
      bottom: 0;
      background:
        repeating-linear-gradient(
          90deg,
          transparent,
          transparent 98px,
          rgba(255, 255, 255, 0.01) 100px
        ),
        repeating-linear-gradient(
          0deg,
          transparent,
          transparent 98px,
          rgba(255, 255, 255, 0.01) 100px
        );
      pointer-events: none;
      z-index: 1;
    }

    .header {
      text-align: center;
      margin-bottom: 3rem;
      z-index: 10;
      position: relative;
    }

    .title {
      font-size: 3rem;
      font-weight: 700;
      margin-bottom: 0.5rem;
      background: linear-gradient(135deg, #64ffda 0%, #00bcd4 50%, #ff4081 100%);
      -webkit-background-clip: text;
      -webkit-text-fill-color: transparent;
      background-clip: text;
      text-align: center;
      letter-spacing: 3px;
      text-shadow: 0 0 30px rgba(100, 255, 218, 0.3);
      animation: titleGlow 3s ease-in-out infinite alternate;
    }

    .subtitle {
      font-size: 1rem;
      font-weight: 400;
      color: rgba(255, 255, 255, 0.6);
      letter-spacing: 1px;
      text-transform: uppercase;
    }

    @keyframes titleGlow {
      0% { filter: brightness(1) saturate(1); }
      100% { filter: brightness(1.2) saturate(1.3); }
    }

    .controls-panel {
      margin-bottom: 3rem;
      z-index: 10;
      position: relative;
    }

    .controls {
      display: flex;
      gap: 1rem;
      align-items: center;
      padding: 1.5rem 2.5rem;
      background: rgba(255, 255, 255, 0.08);
      border-radius: 25px;
      backdrop-filter: blur(20px);
      border: 1px solid rgba(255, 255, 255, 0.15);
      box-shadow:
        0 8px 32px rgba(0, 0, 0, 0.4),
        inset 0 1px 0 rgba(255, 255, 255, 0.1);
      position: relative;
      overflow: hidden;
    }

    .controls::before {
      content: '';
      position: absolute;
      top: 0;
      left: -100%;
      width: 100%;
      height: 100%;
      background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.1), transparent);
      animation: shimmer 3s infinite;
    }

    @keyframes shimmer {
      0% { left: -100%; }
      100% { left: 100%; }
    }

    .speed-controls {
      display: flex;
      gap: 0.5rem;
      align-items: center;
      margin-left: 1rem;
      padding-left: 1rem;
      border-left: 1px solid rgba(255, 255, 255, 0.2);
    }

    .speed-label {
      font-size: 0.9rem;
      color: rgba(255, 255, 255, 0.7);
      margin-right: 0.5rem;
    }

    button {
      padding: 14px 28px;
      font-size: 14px;
      font-weight: 600;
      border: none;
      border-radius: 15px;
      cursor: pointer;
      background: linear-gradient(135deg, #64ffda, #00bcd4);
      color: #000;
      transition: all 0.4s cubic-bezier(0.4, 0, 0.2, 1);
      text-transform: uppercase;
      letter-spacing: 1.5px;
      position: relative;
      overflow: hidden;
      box-shadow: 0 4px 15px rgba(100, 255, 218, 0.2);
    }

    button::before {
      content: '';
      position: absolute;
      top: 0;
      left: -100%;
      width: 100%;
      height: 100%;
      background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.4), transparent);
      transition: left 0.6s ease;
    }

    button:hover::before {
      left: 100%;
    }

    button:hover {
      transform: translateY(-3px) scale(1.02);
      box-shadow: 0 12px 35px rgba(100, 255, 218, 0.4);
      background: linear-gradient(135deg, #7fffd4, #20b2aa);
    }

    button:active {
      transform: translateY(-1px) scale(0.98);
    }

    button:disabled {
      background: rgba(255, 255, 255, 0.1);
      color: rgba(255, 255, 255, 0.3);
      cursor: not-allowed;
      transform: none;
      box-shadow: none;
    }

    .btn-secondary {
      background: linear-gradient(135deg, #ff4081, #ff1744);
      box-shadow: 0 4px 15px rgba(255, 64, 129, 0.2);
    }

    .btn-secondary:hover {
      background: linear-gradient(135deg, #ff6090, #ff4569);
      box-shadow: 0 12px 35px rgba(255, 64, 129, 0.4);
    }

    .btn-small {
      padding: 8px 16px;
      font-size: 12px;
      border-radius: 10px;
    }

    .counter {
      font-size: 1.1rem;
      font-weight: 500;
      margin-left: 2rem;
      color: #64ffda;
      text-shadow: 0 0 10px rgba(100, 255, 218, 0.5);
      padding: 8px 16px;
      background: rgba(100, 255, 218, 0.1);
      border-radius: 12px;
      border: 1px solid rgba(100, 255, 218, 0.2);
    }

    .animation-workspace {
      display: flex;
      flex-direction: column;
      align-items: center;
      gap: 2rem;
      z-index: 10;
      position: relative;
    }

    .container {
      position: relative;
      width: 350px;
      height: 350px;
      background:
        radial-gradient(circle at center, rgba(255, 255, 255, 0.05) 0%, rgba(255, 255, 255, 0.02) 100%),
        rgba(0, 0, 0, 0.2);
      border-radius: 25px;
      backdrop-filter: blur(20px);
      border: 2px solid rgba(255, 255, 255, 0.15);
      box-shadow:
        0 25px 50px rgba(0, 0, 0, 0.5),
        inset 0 1px 0 rgba(255, 255, 255, 0.1),
        0 0 100px rgba(100, 255, 218, 0.1);
      display: flex;
      justify-content: center;
      align-items: center;
      overflow: hidden;
    }

    .container::before {
      content: '';
      position: absolute;
      top: 0;
      left: 0;
      right: 0;
      bottom: 0;
      background:
        repeating-conic-gradient(
          from 0deg at center,
          transparent 0deg,
          rgba(100, 255, 218, 0.03) 1deg,
          transparent 2deg
        );
      border-radius: 25px;
      pointer-events: none;
    }

    .angle-indicator {
      position: absolute;
      top: 15px;
      right: 15px;
      background: rgba(100, 255, 218, 0.2);
      color: #64ffda;
      padding: 8px 12px;
      border-radius: 10px;
      font-size: 0.9rem;
      font-weight: 600;
      border: 1px solid rgba(100, 255, 218, 0.3);
      backdrop-filter: blur(10px);
      z-index: 10;
    }

    .center-point {
      position: absolute;
      width: 6px;
      height: 6px;
      background: radial-gradient(circle, #ffffff, #64ffda);
      border-radius: 50%;
      box-shadow: 0 0 10px rgba(100, 255, 218, 0.8);
      z-index: 5;
    }

    .square {
      width: 120px;
      height: 120px;
      position: absolute;
      border-radius: 12px;
    }

    .square1 {
      background: linear-gradient(45deg, #1e3c72, #2a5298);
      box-shadow: 0 0 30px rgba(30, 60, 114, 0.4);
      border: 1px solid rgba(255, 255, 255, 0.1);
    }

    .rotating-square {
      width: 120px;
      height: 120px;
      background: linear-gradient(45deg, rgba(255, 64, 129, 0.8), rgba(255, 23, 68, 0.8));
      position: absolute;
      transform-origin: center center;
      border-radius: 12px;
      box-shadow: 0 0 40px rgba(255, 64, 129, 0.3);
      border: 1px solid rgba(255, 255, 255, 0.2);
    }

    .dot {
      width: 12px;
      height: 12px;
      background: radial-gradient(circle, #ffffff, #64ffda);
      border-radius: 50%;
      position: absolute;
      top: -6px;
      right: -6px;
      box-shadow: 0 0 15px rgba(100, 255, 218, 0.8);
      border: 2px solid rgba(255, 255, 255, 0.3);
    }

    .fixed-dot {
      width: 12px;
      height: 12px;
      background: radial-gradient(circle, #ff4081, #ff1744);
      border-radius: 50%;
      position: absolute;
      top: -6px;
      right: -6px;
      box-shadow: 0 0 15px rgba(255, 64, 129, 0.8);
      border: 2px solid rgba(255, 255, 255, 0.3);
    }

    .stats-panel {
      display: flex;
      gap: 1.5rem;
      margin-top: 1rem;
    }

    .stat-item {
      display: flex;
      flex-direction: column;
      align-items: center;
      padding: 1rem 1.5rem;
      background: rgba(255, 255, 255, 0.05);
      border-radius: 15px;
      backdrop-filter: blur(10px);
      border: 1px solid rgba(255, 255, 255, 0.1);
      min-width: 80px;
    }

    .stat-label {
      font-size: 0.8rem;
      color: rgba(255, 255, 255, 0.6);
      text-transform: uppercase;
      letter-spacing: 1px;
      margin-bottom: 0.5rem;
    }

    .stat-value {
      font-size: 1.2rem;
      font-weight: 600;
      color: #64ffda;
      text-shadow: 0 0 10px rgba(100, 255, 218, 0.5);
    }

    .status {
      margin-top: 2rem;
      font-size: 1.1rem;
      font-weight: 400;
      color: rgba(255, 255, 255, 0.9);
      text-align: center;
      padding: 1.2rem 2.5rem;
      background:
        linear-gradient(135deg, rgba(100, 255, 218, 0.1) 0%, rgba(255, 64, 129, 0.1) 100%),
        rgba(255, 255, 255, 0.05);
      border-radius: 20px;
      backdrop-filter: blur(15px);
      border: 1px solid rgba(255, 255, 255, 0.15);
      min-height: 60px;
      display: flex;
      align-items: center;
      justify-content: center;
      box-shadow: 0 8px 25px rgba(0, 0, 0, 0.3);
    }

    .footer {
      position: fixed;
      bottom: 20px;
      left: 50%;
      transform: translateX(-50%);
      z-index: 10;
    }

    .controls-hint {
      background: rgba(0, 0, 0, 0.7);
      color: rgba(255, 255, 255, 0.8);
      padding: 8px 16px;
      border-radius: 12px;
      font-size: 0.85rem;
      backdrop-filter: blur(10px);
      border: 1px solid rgba(255, 255, 255, 0.1);
    }

    @keyframes pulse {
      0%, 100% { transform: scale(1); }
      50% { transform: scale(1.05); }
    }

    .completion-flash {
      animation: pulse 0.3s ease-in-out;
    }

    /* Responsive design */
    @media (max-width: 768px) {
      .title {
        font-size: 2rem;
        margin-bottom: 1.5rem;
      }

      .controls {
        flex-direction: column;
        gap: 0.8rem;
        padding: 1rem;
      }

      .counter {
        margin-left: 0;
        margin-top: 0.5rem;
      }

      .container {
        width: 250px;
        height: 250px;
      }

      .square, .rotating-square {
        width: 100px;
        height: 100px;
      }
    }
  </style>
</head>
<body>
  <div class="header">
    <h1 class="title">Quantum Rotation</h1>
    <p class="subtitle">Professional Animation Studio</p>
  </div>

  <div class="controls-panel">
    <div class="controls">
      <button id="startBtn" onclick="startAnimation()">Start</button>
      <button id="pauseBtn" onclick="pauseAnimation()" disabled>Pause</button>
      <button id="resetBtn" onclick="resetAnimation()" class="btn-secondary">Reset</button>

      <div class="speed-controls">
        <span class="speed-label">Speed:</span>
        <button class="btn-small" onclick="setSpeed(0.5)">0.5x</button>
        <button class="btn-small" onclick="setSpeed(1)">1x</button>
        <button class="btn-small" onclick="setSpeed(2)">2x</button>
        <button class="btn-small" onclick="setSpeed(3)">3x</button>
      </div>

      <span class="counter">Corners: <span id="completionCount">0</span></span>
    </div>
  </div>

  <div class="animation-workspace">
    <div class="container">
      <div class="angle-indicator" id="angleIndicator">0°</div>
      <div class="square square1">
        <div class="fixed-dot"></div>
      </div>
      <div class="rotating-square" id="rotatingSquare">
        <div class="dot"></div>
        <div class="rotation-trail" id="rotationTrail"></div>
      </div>
      <div class="center-point"></div>
    </div>

    <div class="stats-panel">
      <div class="stat-item">
        <span class="stat-label">Angle</span>
        <span class="stat-value" id="angleValue">0°</span>
      </div>
      <div class="stat-item">
        <span class="stat-label">Speed</span>
        <span class="stat-value" id="speedValue">1x</span>
      </div>
      <div class="stat-item">
        <span class="stat-label">Time</span>
        <span class="stat-value" id="timeValue">0s</span>
      </div>
    </div>
  </div>

  <div class="status" id="status">Ready to start animation</div>

  <div class="footer">
    <div class="controls-hint">
      <span>⌨️ Keyboard: Space (Start/Pause) • R (Reset) • 1-4 (Speed)</span>
    </div>
  </div></body>
</html>

  <script>
    // Global variables
    const rotatingSquare = document.getElementById("rotatingSquare");
    const startBtn = document.getElementById("startBtn");
    const pauseBtn = document.getElementById("pauseBtn");
    const resetBtn = document.getElementById("resetBtn");
    const completionCountEl = document.getElementById("completionCount");
    const statusEl = document.getElementById("status");
    const angleIndicator = document.getElementById("angleIndicator");
    const angleValue = document.getElementById("angleValue");
    const speedValue = document.getElementById("speedValue");
    const timeValue = document.getElementById("timeValue");

    let angle = 0;
    let isAnimating = false;
    let animationId = null;
    let completionCount = 0;
    let lastCompletionAngle = 0;
    let animationSpeed = 1; // Default speed multiplier
    let startTime = 0;
    let elapsedTime = 0;
    let pausedTime = 0;

    // Initialize corner tracking
    window.passedCorners = new Set();

    // Animation function
    function animate() {
      if (!isAnimating) return;

      angle += 1 * animationSpeed; // degrees per frame with speed control
      rotatingSquare.style.transform = `rotate(${angle}deg)`;

      // Update real-time displays
      updateDisplays();

      // Check for completion
      checkCompletion();

      animationId = requestAnimationFrame(animate);
    }

    // Update real-time displays
    function updateDisplays() {
      const displayAngle = Math.round(angle % 360);
      angleIndicator.textContent = `${displayAngle}°`;
      angleValue.textContent = `${displayAngle}°`;

      // Update timer
      if (isAnimating) {
        const currentTime = Date.now();
        elapsedTime = pausedTime + (currentTime - startTime);
        const seconds = Math.floor(elapsedTime / 1000);
        timeValue.textContent = `${seconds}s`;
      }
    }

    // Check if dot reaches a corner (every 90 degrees)
    function checkCompletion() {
      // Stop after 360 degrees
      if (angle >= 360) {
        if (completionCount < 4) {
          completionCount = 4;
          completionCountEl.textContent = completionCount;
          statusEl.textContent = `✨ Complete! All 4 corners reached!`;
          flashCompletion();
        }
        pauseAnimation();
        return;
      }

      // Check for corner positions (90°, 180°, 270°)
      const corners = [90, 180, 270];

      for (let corner of corners) {
        // Check if we just passed this corner
        if (angle >= corner && !window.passedCorners.has(corner)) {
          window.passedCorners.add(corner);
          completionCount++;
          completionCountEl.textContent = completionCount;

          // Determine which corner was reached
          const cornerNames = {90: 'Bottom-Right', 180: 'Bottom-Left', 270: 'Top-Left'};
          const cornerName = cornerNames[corner];

          statusEl.textContent = `${completionCount} corner${completionCount !== 1 ? 's' : ''} reached! (${cornerName})`;

          // Visual feedback
          flashCompletion();
          break;
        }
      }
    }

    // Visual feedback for completion
    function flashCompletion() {
      rotatingSquare.style.boxShadow = '0 0 50px rgba(100, 255, 218, 0.8)';
      setTimeout(() => {
        rotatingSquare.style.boxShadow = '0 0 40px rgba(255, 64, 129, 0.3)';
      }, 300);
    }

    // Set animation speed
    function setSpeed(speed) {
      animationSpeed = speed;
      speedValue.textContent = `${speed}x`;

      // Update button states
      document.querySelectorAll('.speed-controls .btn-small').forEach(btn => {
        btn.style.background = 'rgba(255, 255, 255, 0.1)';
        btn.style.color = 'rgba(255, 255, 255, 0.7)';
      });

      // Highlight active speed button
      event.target.style.background = 'linear-gradient(135deg, #64ffda, #00bcd4)';
      event.target.style.color = '#000';

      statusEl.textContent = `Speed set to ${speed}x`;
    }

    // Start animation
    function startAnimation() {
      isAnimating = true;
      startBtn.disabled = true;
      pauseBtn.disabled = false;

      if (startTime === 0) {
        startTime = Date.now();
      } else {
        startTime = Date.now() - pausedTime;
      }

      statusEl.textContent = `🚀 Animation running at ${animationSpeed}x speed`;
      animate();
    }

    // Pause animation
    function pauseAnimation() {
      isAnimating = false;
      startBtn.disabled = false;
      pauseBtn.disabled = true;

      if (startTime > 0) {
        pausedTime = elapsedTime;
      }

      statusEl.textContent = "⏸️ Animation paused";
      if (animationId) {
        cancelAnimationFrame(animationId);
      }
    }

    // Reset animation
    function resetAnimation() {
      isAnimating = false;
      angle = 0;
      completionCount = 0;
      lastCompletionAngle = 0;
      startTime = 0;
      elapsedTime = 0;
      pausedTime = 0;

      // Clear corner tracking
      window.passedCorners.clear();

      // Reset displays
      rotatingSquare.style.transform = `rotate(0deg)`;
      completionCountEl.textContent = "0";
      angleIndicator.textContent = "0°";
      angleValue.textContent = "0°";
      timeValue.textContent = "0s";
      statusEl.textContent = "🔄 Reset complete - Ready to start";

      startBtn.disabled = false;
      pauseBtn.disabled = true;

      if (animationId) {
        cancelAnimationFrame(animationId);
      }
    }

    // Enhanced keyboard controls
    document.addEventListener('keydown', function(event) {
      switch(event.code) {
        case 'Space':
          event.preventDefault();
          if (isAnimating) {
            pauseAnimation();
          } else {
            startAnimation();
          }
          break;
        case 'KeyR':
          event.preventDefault();
          resetAnimation();
          break;
        case 'Digit1':
          event.preventDefault();
          setSpeed(0.5);
          break;
        case 'Digit2':
          event.preventDefault();
          setSpeed(1);
          break;
        case 'Digit3':
          event.preventDefault();
          setSpeed(2);
          break;
        case 'Digit4':
          event.preventDefault();
          setSpeed(3);
          break;
        case 'Escape':
          event.preventDefault();
          pauseAnimation();
          break;
      }
    });

    // Initialize with professional styling
    function initializeApp() {
      statusEl.textContent = "🎯 Professional Animation Studio Ready";
      speedValue.textContent = "1x";

      // Set default speed button active
      const defaultSpeedBtn = document.querySelector('.speed-controls .btn-small:nth-child(3)');
      if (defaultSpeedBtn) {
        defaultSpeedBtn.style.background = 'linear-gradient(135deg, #64ffda, #00bcd4)';
        defaultSpeedBtn.style.color = '#000';
      }
    }

    // Initialize app
    initializeApp();
  </script>
</body>
</html>
