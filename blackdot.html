<!DOCTYPE html>
<html lang="en">
<head>
  <meta charset="UTF-8" />
  <meta name="viewport" content="width=device-width, initial-scale=1.0"/>
  <title>Interactive Rotating Squares</title>
  <style>
    body {
      display: flex;
      flex-direction: column;
      justify-content: center;
      align-items: center;
      height: 100vh;
      margin: 0;
      background: #f0f0f0;
      font-family: Arial, sans-serif;
    }

    .controls {
      margin-bottom: 20px;
      display: flex;
      gap: 10px;
      align-items: center;
    }

    button {
      padding: 10px 20px;
      font-size: 16px;
      border: none;
      border-radius: 5px;
      cursor: pointer;
      background-color: #4CAF50;
      color: white;
      transition: background-color 0.3s;
    }

    button:hover {
      background-color: #45a049;
    }

    button:disabled {
      background-color: #cccccc;
      cursor: not-allowed;
    }

    .counter {
      font-size: 18px;
      font-weight: bold;
      margin-left: 20px;
      color: #333;
    }

    .container {
      position: relative;
      width: 400px;
      height: 200px;
      border: 2px solid #ddd;
      border-radius: 10px;
      background: white;
    }

    .square {
      width: 100px;
      height: 100px;
      position: absolute;
      border-radius: 5px;
    }

    .square1 {
      left: 150px;
      top: 50px;
      background-color: #2196F3;
    }

    .rotating-square {
      width: 100px;
      height: 100px;
      background-color: rgba(255, 0, 0, 0.7);
      position: absolute;
      left: 150px;
      top: 50px;
      transform-origin: center center;
      border-radius: 5px;
    }

    .dot {
      width: 10px;
      height: 10px;
      background-color: black;
      border-radius: 50%;
      position: absolute;
      top: -5px;
      right: -5px;
    }

    .fixed-dot {
      width: 10px;
      height: 10px;
      background-color: red;
      border-radius: 50%;
      position: absolute;
      top: -5px;
      right: -5px;
    }

    .status {
      margin-top: 20px;
      font-size: 16px;
      color: #666;
    }
  </style>
</head>
<body>
  <div class="controls">
    <button id="startBtn" onclick="startAnimation()">Start</button>
    <button id="pauseBtn" onclick="pauseAnimation()" disabled>Pause</button>
    <button id="resetBtn" onclick="resetAnimation()">Reset</button>
    <span class="counter">Corners Reached: <span id="completionCount">0</span></span>
  </div>

  <div class="container">
    <div class="square square1">
      <div class="fixed-dot"></div>
    </div>
    <div class="rotating-square" id="rotatingSquare">
      <div class="dot"></div>
    </div>
  </div>

  <div class="status" id="status">Ready to start</div>

  <script>
    // Global variables
    const rotatingSquare = document.getElementById("rotatingSquare");
    const startBtn = document.getElementById("startBtn");
    const pauseBtn = document.getElementById("pauseBtn");
    const resetBtn = document.getElementById("resetBtn");
    const completionCountEl = document.getElementById("completionCount");
    const statusEl = document.getElementById("status");

    let angle = 0;
    let isAnimating = false;
    let animationId = null;
    let completionCount = 0;
    let lastCompletionAngle = 0;

    // Animation function
    function animate() {
      if (!isAnimating) return;

      angle += 2; // degrees per frame
      rotatingSquare.style.transform = `rotate(${angle}deg)`;

      // Check for completion (full 360 degree rotation)
      checkCompletion();

      animationId = requestAnimationFrame(animate);
    }

    // Check if dot reaches a corner (every 90 degrees)
    function checkCompletion() {
      // Normalize angle to 0-360 range
      const normalizedAngle = angle % 360;

      // Check for corner positions (0°, 90°, 180°, 270°)
      const corners = [0, 90, 180, 270];
      const tolerance = 2; // degrees tolerance for corner detection

      for (let corner of corners) {
        // Check if we're at a corner and haven't counted this corner yet
        if (Math.abs(normalizedAngle - corner) <= tolerance) {
          const cornerKey = `${Math.floor(angle / 90)}_${corner}`;

          // Avoid counting the same corner multiple times
          if (!window.countedCorners) {
            window.countedCorners = new Set();
          }

          if (!window.countedCorners.has(cornerKey)) {
            window.countedCorners.add(cornerKey);
            completionCount++;
            completionCountEl.textContent = completionCount;

            // Determine which corner was reached
            const cornerNames = {0: 'Top-Right', 90: 'Bottom-Right', 180: 'Bottom-Left', 270: 'Top-Left'};
            const cornerName = cornerNames[corner];

            statusEl.textContent = `${completionCount} corner${completionCount !== 1 ? 's' : ''} reached! (${cornerName})`;

            // Visual feedback
            flashCompletion();
          }
          break;
        }
      }
    }

    // Visual feedback for completion
    function flashCompletion() {
      rotatingSquare.style.boxShadow = '0 0 20px #ff0000';
      setTimeout(() => {
        rotatingSquare.style.boxShadow = 'none';
      }, 300);
    }

    // Start animation
    function startAnimation() {
      isAnimating = true;
      startBtn.disabled = true;
      pauseBtn.disabled = false;
      statusEl.textContent = "Animation running...";
      animate();
    }

    // Pause animation
    function pauseAnimation() {
      isAnimating = false;
      startBtn.disabled = false;
      pauseBtn.disabled = true;
      statusEl.textContent = "Animation paused";
      if (animationId) {
        cancelAnimationFrame(animationId);
      }
    }

    // Reset animation
    function resetAnimation() {
      isAnimating = false;
      angle = 0;
      completionCount = 0;
      lastCompletionAngle = 0;

      // Clear corner tracking
      if (window.countedCorners) {
        window.countedCorners.clear();
      }

      rotatingSquare.style.transform = `rotate(0deg)`;
      completionCountEl.textContent = "0";
      statusEl.textContent = "Reset complete - Ready to start";

      startBtn.disabled = false;
      pauseBtn.disabled = true;

      if (animationId) {
        cancelAnimationFrame(animationId);
      }
    }

    // Keyboard controls
    document.addEventListener('keydown', function(event) {
      switch(event.code) {
        case 'Space':
          event.preventDefault();
          if (isAnimating) {
            pauseAnimation();
          } else {
            startAnimation();
          }
          break;
        case 'KeyR':
          resetAnimation();
          break;
      }
    });

    // Initialize
    statusEl.textContent = "Ready to start (Space: Start/Pause, R: Reset)";
  </script>
</body>
</html>
