<!DOCTYPE html>
<html lang="en">
<head>
  <meta charset="UTF-8" />
  <meta name="viewport" content="width=device-width, initial-scale=1.0"/>
  <title>Interactive Rotating Squares</title>
  <style>
    body {
      display: flex;
      flex-direction: column;
      justify-content: center;
      align-items: center;
      height: 100vh;
      margin: 0;
      background: #f0f0f0;
      font-family: Arial, sans-serif;
    }

    .controls {
      margin-bottom: 20px;
      display: flex;
      gap: 10px;
      align-items: center;
    }

    button {
      padding: 10px 20px;
      font-size: 16px;
      border: none;
      border-radius: 5px;
      cursor: pointer;
      background-color: #4CAF50;
      color: white;
      transition: background-color 0.3s;
    }

    button:hover {
      background-color: #45a049;
    }

    button:disabled {
      background-color: #cccccc;
      cursor: not-allowed;
    }

    .counter {
      font-size: 18px;
      font-weight: bold;
      margin-left: 20px;
      color: #333;
    }

    .container {
      position: relative;
      width: 400px;
      height: 200px;
      border: 2px solid #ddd;
      border-radius: 10px;
      background: white;
    }

    .square {
      width: 100px;
      height: 100px;
      position: absolute;
      border-radius: 5px;
    }

    .square1 {
      left: 50px;
      top: 50px;
      background-color: #2196F3;
    }

    .rotating-square {
      width: 100px;
      height: 100px;
      background-color: rgba(255, 0, 0, 0.7);
      position: absolute;
      left: 200px;
      top: 50px;
      transform-origin: center center;
      border-radius: 5px;
    }

    .dot {
      width: 10px;
      height: 10px;
      background-color: black;
      border-radius: 50%;
      position: absolute;
      top: -5px;
      right: -5px;
    }

    .fixed-dot {
      width: 10px;
      height: 10px;
      background-color: red;
      border-radius: 50%;
      position: absolute;
      top: -5px;
      right: -5px;
    }

    .status {
      margin-top: 20px;
      font-size: 16px;
      color: #666;
    }
  </style>
</head>
<body>
  <div class="controls">
    <button id="startBtn" onclick="startAnimation()">Start</button>
    <button id="pauseBtn" onclick="pauseAnimation()" disabled>Pause</button>
    <button id="resetBtn" onclick="resetAnimation()">Reset</button>
    <span class="counter">Completions: <span id="completionCount">0</span></span>
  </div>

  <div class="container">
    <div class="square square1">
      <div class="fixed-dot"></div>
    </div>
    <div class="rotating-square" id="rotatingSquare">
      <div class="dot"></div>
    </div>
  </div>

  <div class="status" id="status">Ready to start</div>

  <script>
    // Global variables
    const rotatingSquare = document.getElementById("rotatingSquare");
    const startBtn = document.getElementById("startBtn");
    const pauseBtn = document.getElementById("pauseBtn");
    const resetBtn = document.getElementById("resetBtn");
    const completionCountEl = document.getElementById("completionCount");
    const statusEl = document.getElementById("status");

    let angle = 0;
    let isAnimating = false;
    let animationId = null;
    let completionCount = 0;
    let lastCompletionAngle = 0;

    // Animation function
    function animate() {
      if (!isAnimating) return;

      angle += 2; // degrees per frame
      rotatingSquare.style.transform = `rotate(${angle}deg)`;

      // Check for completion (full 360 degree rotation)
      checkCompletion();

      animationId = requestAnimationFrame(animate);
    }

    // Check if a full rotation is completed
    function checkCompletion() {
      const currentRotations = Math.floor(angle / 360);
      const lastRotations = Math.floor(lastCompletionAngle / 360);

      if (currentRotations > lastRotations) {
        completionCount++;
        completionCountEl.textContent = completionCount;
        statusEl.textContent = `${completionCount} square${completionCount !== 1 ? 's' : ''} completed!`;
        lastCompletionAngle = angle;

        // Visual feedback
        flashCompletion();
      }
    }

    // Visual feedback for completion
    function flashCompletion() {
      rotatingSquare.style.boxShadow = '0 0 20px #ff0000';
      setTimeout(() => {
        rotatingSquare.style.boxShadow = 'none';
      }, 300);
    }

    // Start animation
    function startAnimation() {
      isAnimating = true;
      startBtn.disabled = true;
      pauseBtn.disabled = false;
      statusEl.textContent = "Animation running...";
      animate();
    }

    // Pause animation
    function pauseAnimation() {
      isAnimating = false;
      startBtn.disabled = false;
      pauseBtn.disabled = true;
      statusEl.textContent = "Animation paused";
      if (animationId) {
        cancelAnimationFrame(animationId);
      }
    }

    // Reset animation
    function resetAnimation() {
      isAnimating = false;
      angle = 0;
      completionCount = 0;
      lastCompletionAngle = 0;

      rotatingSquare.style.transform = `rotate(0deg)`;
      completionCountEl.textContent = "0";
      statusEl.textContent = "Reset complete - Ready to start";

      startBtn.disabled = false;
      pauseBtn.disabled = true;

      if (animationId) {
        cancelAnimationFrame(animationId);
      }
    }

    // Keyboard controls
    document.addEventListener('keydown', function(event) {
      switch(event.code) {
        case 'Space':
          event.preventDefault();
          if (isAnimating) {
            pauseAnimation();
          } else {
            startAnimation();
          }
          break;
        case 'KeyR':
          resetAnimation();
          break;
      }
    });

    // Initialize
    statusEl.textContent = "Ready to start (Space: Start/Pause, R: Reset)";
  </script>
</body>
</html>
